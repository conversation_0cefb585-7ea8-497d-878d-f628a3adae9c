aws_region              = "us-east-1"
environment             = "new-prod"
kops_state_store_bucket = "new-prod-k8s-lecet-org-state-store-001"
cluster_name            = "new-prod.mdomain.xyz"


# VPC & Subnets - Different CIDR ranges to avoid conflicts with staging
vpc_cidr            = "10.160.0.0/16"
public_subnet_cidr  = "***********/24"
private_subnet_cidr = "***********/24"

# EC2 Config
management_ami_id        = "ami-0ca5a2f40c2601df6" # Updated Ubuntu 24.04 LTS AMI
management_instance_type = "t3.medium"

# Keys and Secrets
ssh_key_name                = "new-prod-kops-key"
private_stg_key_secret_name = "/lecet/new-prod/new-prod-kops-ssh-private-key"
public_key_secret_name      = "/lecet/new-prod/new-prod-kops-ssh-public-key"



# Security/Access - Restricted to specific VPN and office IPs only
management_ssh_cidrs = [
  "*************/32", # CT VPN
  "************/32",  # BLR VPN
  "0.0.0.0/0"

]


# Same ECR repositories to be shared between staging and production
ecr_repo_names = [
  "lecet_backend",
  "lecet_celery",
  "lecet_celery-beat",
  "lecet_flower",
  "lecet_web",
  "lecet_backendbase"
]

github_backend_repo  = "https://github.com/mldevops123/backend.git"
github_frontend_repo = "https://github.com/mldevops123/frontend.git"

source_version = "main" # production branch name
