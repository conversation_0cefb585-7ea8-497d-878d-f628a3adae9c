# Parameter Store variables for web application
resource "aws_ssm_parameter" "web_parameters" {
  for_each = {
    "backendURL"                                        = "https://api-staging.lecet.org"
    "REACT_APP_SHOW_MEPC_FEATURES"                      = "true"
    "REACT_APP_MEPC_CLASSIC_URL"                        = " "
    "REACT_APP_CONTRACTOR_LOGIN_ENABLED"                = "true"
    "REACT_APP_MEPC_CLASSIC_SIGNUP_URL"                 = "false"
    "REACT_APP_SHOW_PROJECT_VALUE_CONFIG_ENABLED"       = "true"
    "REACT_APP_SHOW_MY_TEAMS_ENABLED"                   = "true"
    "REACT_APP_SHOW_BNS"                                = "true"
    "REACT_APP_SHOW_NEW_FILTERS"                        = "true"
    "REACT_APP_SHOW_NEW_MORE_FILTERS"                   = "true"
    "REACT_APP_SHOW_TABLEAU"                            = "false"
    "REACT_APP_SHOW_PODCAST"                            = "true"
    "REACT_APP_LOCAL_STORAGE_ENCIPHER"                  = "#prod~FL4Md}Uwo|0R5d3;5}Ly7+Y;*!"
    "REACT_APP_VERSION"                                 = "$npm_package_version"
    "REACT_APP_NAME"                                    = "$npm_package_name"
    "REACT_APP_SHOW_HELPCENTER"                         = "true"
    "REACT_APP_ENABLE_SPECTATE"                         = "true"
    "REACT_APP_GOOGLE_ANALYTICS_KEY"                    = "UA-58798577-3"
    "REACT_APP_GOOGLE_MAP_KEY"                          = "AIzaSyDZv529gWBw-C51JNNtPXo8xi8LdNzdoAg"
    "GENERATE_SOURCEMAP"                                = "false"
    "REACT_APP_SHOW_LABORER_SIGN_UP"                    = "true"
    "REACT_APP_SHOW_MKT_EXPORT"                         = "true"
    "REACT_APP_DISABLE_DISCOVER_MKT_EXPORT"             = "true"
    "REACT_APP_SHOW_MENU_SETTINGS"                      = "true"
    "REACT_APP_BACKEND_URL_PORT"                        = "8080"
    "REACT_APP_SHOW_NEW_DETAILS"                        = "true"
    "REACT_APP_DASHBOARD_STATIC_PAGINATION"             = "true"
    "REACT_APP_SHOW_NEW_USER_JURISDICTION_POPUP"        = "true"
    "REACT_APP_SHOW_INTEND_TO_BID"                      = "true"
    "REACT_APP_SHOW_API_DOC"                            = "false"
    "REACT_APP_SHOW_NEW_NOTIFICATION_SETTINGS"          = "true"
    "REACT_APP_SHOW_DEFAULT_SAVED_SEARCH_FILTER"        = "false"
    "REACT_APP_ENABLE_MULTIPLE_COUNTIES"                = "true"
    "REACT_APP_SHOW_MARKET_SHARE_REPORT"                = "true"
    "REACT_APP_SHOW_OLD_DI_DASHBOARD"                   = "false"
    "REACT_APP_SHOW_ENGAGEMENT_TALLY_COLUMN"            = "true"
    "REACT_APP_SHOW_MARKET_SHARE_BACKUP_RESTORE"        = "true"
    "REACT_APP_ENABLE_ENGAGEMENT_TRACKED_TALLY_SORT"    = "true"
    "REACT_APP_GOOGLE_ANALYTICS_FOUR_TAG"               = "G-WPWEW32DCV"
    "REACT_APP_FIREBASE_CONFIG_APP_ID"                  = "1:442704824790:web:7eac47f35a225e0c957c77"
    "REACT_APP_FIREBASE_CONFIG_MESSAGING_SENDER_ID"     = "442704824790"
    "REACT_APP_FIREBASE_CONFIG_STORAGE_BUCKET"          = "lecet-ee7e2.appspot.com"
    "REACT_APP_FIREBASE_CONFIG_PROJECT_ID"              = "lecet-ee7e2"
    "REACT_APP_FIREBASE_CONFIG_DATABASE_URL"            = "https://lecet-ee7e2.firebaseio.com"
    "REACT_APP_FIREBASE_CONFIG_AUTH_DOMAIN"             = "lecet-ee7e2.firebaseapp.com"
    "REACT_APP_FIREBASE_CONFIG_API_KEY"                 = "AIzaSyBrA2lrNd-rLSE6SNyklYzgPDPusMSYQe0"
    "REACT_APP_ENABLE_HIDE_UNHIDE_PROJECT"              = "true"
    "REACT_APP_SHOW_ANALYTICS"                         = "true"
    "REACT_APP_SHOW_ASSOCIATED_PROJECTS_STAGE_FILTER"   = "true"
    "REACT_APP_SHOW_ENGAGEMENT_REQUESTS"                = "true"
    "REACT_APP_SHOW_ENGAGEMENT_REQUEST_SUB_LISTS"       = "true"
    "REACT_APP_SHOW_IIR"                                = "true"
    "REACT_APP_SHOW_NEW_UI_SWITCH"                     = "true"
    

  }

  name  = "/lecet-web/new-staging/${each.key}"
  type  = "String"
  value = each.value

  tags = {
    Environment = var.environment

  }
}





