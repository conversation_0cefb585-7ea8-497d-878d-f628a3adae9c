# Automated SSH Keypair Generation and Management
# This file creates a new SSH keypair automatically and stores it securely

# Generate a new SSH private key
resource "tls_private_key" "stg_kops_ssh_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

# Create AWS Key Pair using the generated public key
resource "aws_key_pair" "stg_kops_keypair" {
  key_name   = var.ssh_key_name
  public_key = tls_private_key.stg_kops_ssh_key.public_key_openssh

  tags = {
    Name        = var.ssh_key_name
    Environment = var.environment
    Purpose     = "kOps Cluster Access"
    CreatedBy   = "Terraform"
  }
}

# Store the private key in AWS Secrets Manager
resource "aws_secretsmanager_secret" "stg_kops_private_key" {
  name                           = var.private_stg_key_secret_name
  description                    = "Auto-generated SSH private key for kOps cluster access"
  recovery_window_in_days        = 0
  force_overwrite_replica_secret = true

  tags = {
    Name        = "kOps SSH Private Key - ${var.environment}"
    Environment = var.environment
    Purpose     = "kOps Cluster Access"
    CreatedBy   = "Terraform"
  }
}

resource "aws_secretsmanager_secret_version" "stg_kops_private_key" {
  secret_id     = aws_secretsmanager_secret.stg_kops_private_key.id
  secret_string = tls_private_key.stg_kops_ssh_key.private_key_pem
}

# Store the public key in Secrets Manager as well (for reference)
resource "aws_secretsmanager_secret" "stg_kops_public_key" {
  name                           = var.public_key_secret_name
  description                    = "Auto-generated SSH public key for kOps cluster access"
  recovery_window_in_days        = 0
  force_overwrite_replica_secret = true

  tags = {
    Name        = "kOps SSH Public Key - ${var.environment}"
    Environment = var.environment
    Purpose     = "kOps Cluster Access"
    CreatedBy   = "Terraform"
  }
}

resource "aws_secretsmanager_secret_version" "stg_kops_public_key" {
  secret_id     = aws_secretsmanager_secret.stg_kops_public_key.id
  secret_string = tls_private_key.stg_kops_ssh_key.public_key_openssh
}

# Data source to retrieve the private key for use in other resources
data "aws_secretsmanager_secret_version" "stg_kops_private_key" {
  secret_id  = aws_secretsmanager_secret.stg_kops_private_key.id
  depends_on = [aws_secretsmanager_secret_version.stg_kops_private_key]
}

# Automatically save the PEM key to a local file with proper formatting
resource "local_file" "stg_ssh_private_key_pem" {
  content         = tls_private_key.stg_kops_ssh_key.private_key_pem
  filename        = "${path.module}/${var.ssh_key_name}.pem"
  file_permission = "0600"

  depends_on = [tls_private_key.stg_kops_ssh_key]
}

# Outputs for reference
output "keypair_name" {
  description = "Name of the auto-generated AWS keypair"
  value       = aws_key_pair.stg_kops_keypair.key_name
}
