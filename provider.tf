provider "aws" {
  region = var.aws_region
}

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }


  }

  backend "s3" {
    bucket  = "stg-kops-terraform-state-bucket-001" # <-- Replace with your manually created bucket name
    key     = "new-stg/terraform.tfstate"
    region  = "us-east-1"
    encrypt = true

  }
}
