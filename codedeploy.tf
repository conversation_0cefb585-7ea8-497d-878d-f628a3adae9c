# -------------------------------------------------------------------
# CodeBuild Projects for Deploy (Kubernetes deployment)
# -------------------------------------------------------------------

resource "aws_codebuild_project" "lecet_backend_deploy" {
  name         = "new-prod-backend-deploy"
  description  = "Backend deployment for new-prod environment"
  service_role = aws_iam_role.prod_codebuild.arn

  source {
    type                = "GITHUB"
    location            = var.github_backend_repo
    git_clone_depth     = 1
    buildspec           = "deployspec.yml"
    report_build_status = false
  }

  environment {
    compute_type    = "BUILD_GENERAL1_MEDIUM"
    image           = "aws/codebuild/standard:7.0"
    type            = "LINUX_CONTAINER"
    privileged_mode = false

    environment_variable {
      name  = "KUBECONFIGDATA"
      value = "/lecet-web/new-prod/KUBECONFIGDATA"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "KUBECONFIGKEY"
      value = "/lecet-web/new-prod/KUBECONFIGKEY"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "KUBECONFIGCERT"
      value = "/lecet-web/new-prod/KUBECONFIGCERT"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "CLUSTER"
      value = local.cluster_endpoint
    }


  }

  logs_config {
    cloudwatch_logs {
      group_name  = aws_cloudwatch_log_group.backend_deploy_logs.name
      stream_name = "deploy"
    }
  }

  artifacts {
    type = "NO_ARTIFACTS"
  }

  source_version = var.source_version

  tags = {
    Environment = var.environment
  }
}

resource "aws_codebuild_project" "lecet_web_deploy" {
  name         = "new-prod-web-deploy"
  description  = "Frontend deployment for new-prod environment"
  service_role = aws_iam_role.prod_codebuild.arn

  source {
    type                = "GITHUB"
    location            = var.github_frontend_repo
    git_clone_depth     = 1
    buildspec           = "deployspec.yml"
    report_build_status = false
  }

  environment {
    compute_type    = "BUILD_GENERAL1_MEDIUM"
    image           = "aws/codebuild/standard:7.0"
    type            = "LINUX_CONTAINER"
    privileged_mode = false

    environment_variable {
      name  = "KUBECONFIGDATA"
      value = "/lecet-web/new-prod/KUBECONFIGDATA"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "KUBECONFIGKEY"
      value = "/lecet-web/new-prod/KUBECONFIGKEY"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "KUBECONFIGCERT"
      value = "/lecet-web/new-prod/KUBECONFIGCERT"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "CLUSTER"
      value = local.cluster_endpoint
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = aws_cloudwatch_log_group.web_deploy_logs.name
      stream_name = "deploy"
    }
  }

  artifacts {
    type = "NO_ARTIFACTS"
  }

  source_version = var.source_version

  tags = {
    Environment = var.environment
  }
}



