# Region & Environment
variable "aws_region" {
  description = "AWS region"
  type        = string
}

variable "environment" {
  description = "Environment name (staging, prod, etc.)"
  type        = string
}

# kOps State Bucket
variable "kops_state_store_bucket" {
  description = "S3 bucket for kOps state"
  type        = string
}

# VPC & Subnets
variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
}

variable "public_subnet_cidr" {
  description = "CIDR block for the public subnet"
  type        = string
}

variable "private_subnet_cidr" {
  description = "CIDR block for the private subnet"
  type        = string
}

# Management EC2 Details
variable "management_ami_id" {
  description = "AMI ID for the kOps management server"
  type        = string
}

variable "ssh_key_name" {
  description = "Name of the AWS key pair to use for SSH "
  type        = string
  default     = null
}

variable "private_stg_key_secret_name" {
  description = "AWS Secrets Manager secret name for the private SSH key"
  type        = string

}

variable "public_key_secret_name" {
  description = "AWS Secrets Manager secret name for the public SSH key"
  type        = string

}

variable "management_instance_type" {
  description = "EC2 instance type for the kOps management server"
  type        = string
}

# Network Security Settings
variable "management_ssh_cidrs" {
  description = "CIDRs allowed for SSH access to the management server"
  type        = list(string)
}

variable "ecr_repo_names" {
  description = "List of ECR repository names"
  type        = list(string)
}

variable "github_backend_repo" {
  description = "URL of the backend GitHub repository"
  type        = string
}

variable "github_frontend_repo" {
  description = "URL of the frontend GitHub repository"
  type        = string
}

variable "source_version" {
  description = "value for source version"
  type        = string
}

variable "cluster_name" {
  description = "value for source version"
  type        = string
}
