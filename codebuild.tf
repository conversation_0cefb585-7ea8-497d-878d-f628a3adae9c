# -------------------------------------------------------------------
# CodeBuild Projects for Build (Docker image creation and ECR push)
# -------------------------------------------------------------------

# Backend Base build project
resource "aws_codebuild_project" "lecet_backend_base_build" {
  name         = "new-prod-backend-base-build"
  description  = "Build for backend base image using Dockerfile.base"
  service_role = aws_iam_role.codebuild.arn

  source {
    type            = "GITHUB"
    location        = var.github_backend_repo
    git_clone_depth = 1
    buildspec       = "backend_base_buildspec.yml" # Reference your new buildspec file here
  }

  source_version = var.source_version

  environment {
    compute_type    = "BUILD_GENERAL1_SMALL"
    image           = "aws/codebuild/standard:6.0"
    type            = "LINUX_CONTAINER"
    privileged_mode = true

    environment_variable {
      name  = "ECR_REPO_NAME"
      value = "lecet_backendbase"
    }

    environment_variable {
      name  = "IMAGE_TAG"
      value = "new-prod"
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = aws_cloudwatch_log_group.backend_base_build_logs.name
      stream_name = "new_prod"
    }
  }

  artifacts {
    type = "NO_ARTIFACTS"
  }

  tags = {
    Environment = var.environment
  }
}


# Backend main build project
resource "aws_codebuild_project" "lecet_backend_build" {
  name         = "new-prod-backend-build"
  description  = "Backend build for new-prod environment"
  service_role = aws_iam_role.codebuild.arn

  source {
    type                = "GITHUB"
    location            = var.github_backend_repo
    git_clone_depth     = 1
    buildspec           = "buildspec.yml"
    report_build_status = false
  }

  source_version = var.source_version

  environment {
    compute_type    = "BUILD_GENERAL1_SMALL"
    image           = "aws/codebuild/standard:6.0"
    type            = "LINUX_CONTAINER"
    privileged_mode = true

    environment_variable {
      name  = "IMAGE_TAG"
      value = "new-prod"
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = aws_cloudwatch_log_group.backend_build_logs.name
      stream_name = "build"
    }
  }

  artifacts {
    type = "NO_ARTIFACTS"
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_codebuild_project" "lecet_web" {
  name         = "new-prod-web-build"
  description  = "Frontend image build for new-prod environment"
  service_role = aws_iam_role.codebuild.arn

  source {
    type            = "GITHUB"
    location        = var.github_frontend_repo
    git_clone_depth = 1
    buildspec       = "buildspec.yml"
  }

  source_version = var.source_version

  environment {
    compute_type    = "BUILD_GENERAL1_MEDIUM"
    image           = "aws/codebuild/standard:6.0"
    type            = "LINUX_CONTAINER"
    privileged_mode = true

    environment_variable {
      name  = "backendURL"
      value = "/lecet-web/new-prod/backendURL"
      type  = "PARAMETER_STORE"
    }
    environment_variable {
      name  = "REACT_APP_SHOW_MEPC_FEATURES"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_MEPC_FEATURES"
      type  = "PARAMETER_STORE"
    }
    environment_variable {
      name  = "REACT_APP_MEPC_CLASSIC_URL"
      value = "/lecet-web/new-prod/REACT_APP_MEPC_CLASSIC_URL"
      type  = "PARAMETER_STORE"
    }
    environment_variable {
      name  = "REACT_APP_CONTRACTOR_LOGIN_ENABLED"
      value = "/lecet-web/new-prod/REACT_APP_CONTRACTOR_LOGIN_ENABLED"
      type  = "PARAMETER_STORE"
    }
    environment_variable {
      name  = "REACT_APP_MEPC_CLASSIC_SIGNUP_URL"
      value = "/lecet-web/new-prod/REACT_APP_MEPC_CLASSIC_SIGNUP_URL"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_PROJECT_VALUE_CONFIG_ENABLED"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_PROJECT_VALUE_CONFIG_ENABLED"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_MY_TEAMS_ENABLED"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_MY_TEAMS_ENABLED"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_SHOW_BNS"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_BNS"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_SHOW_NEW_FILTERS"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_NEW_FILTERS"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_SHOW_NEW_MORE_FILTERS"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_NEW_MORE_FILTERS"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_TABLEAU"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_TABLEAU"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_PODCAST"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_PODCAST"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_LOCAL_STORAGE_ENCIPHER"
      value = "/lecet-web/new-prod/REACT_APP_LOCAL_STORAGE_ENCIPHER"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_VERSION"
      value = "/lecet-web/new-prod/REACT_APP_VERSION"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_NAME"
      value = "/lecet-web/new-prod/REACT_APP_NAME"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_SHOW_HELPCENTER"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_HELPCENTER"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_ENABLE_SPECTATE"
      value = "/lecet-web/new-prod/REACT_APP_ENABLE_SPECTATE"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_GOOGLE_ANALYTICS_KEY"
      value = "/lecet-web/new-prod/REACT_APP_GOOGLE_ANALYTICS_KEY"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_GOOGLE_MAP_KEY"
      value = "/lecet-web/new-prod/REACT_APP_GOOGLE_MAP_KEY"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "GENERATE_SOURCEMAP"
      value = "/lecet-web/new-prod/GENERATE_SOURCEMAP"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_SHOW_LABORER_SIGN_UP"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_LABORER_SIGN_UP"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_MKT_EXPORT"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_MKT_EXPORT"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_DISABLE_DISCOVER_MKT_EXPORT"
      value = "/lecet-web/new-prod/REACT_APP_DISABLE_DISCOVER_MKT_EXPORT"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_MENU_SETTINGS"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_MENU_SETTINGS"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_BACKEND_URL_PORT"
      value = "/lecet-web/new-prod/REACT_APP_BACKEND_URL_PORT"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_NEW_DETAILS"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_NEW_DETAILS"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_DASHBOARD_STATIC_PAGINATION"
      value = "/lecet-web/new-prod/REACT_APP_DASHBOARD_STATIC_PAGINATION"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_NEW_USER_JURISDICTION_POPUP"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_NEW_USER_JURISDICTION_POPUP"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_INTEND_TO_BID"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_INTEND_TO_BID"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_API_DOC"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_API_DOC"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_NEW_NOTIFICATION_SETTINGS"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_NEW_NOTIFICATION_SETTINGS"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_DEFAULT_SAVED_SEARCH_FILTER"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_DEFAULT_SAVED_SEARCH_FILTER"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_ENABLE_MULTIPLE_COUNTIES"
      value = "/lecet-web/new-prod/REACT_APP_ENABLE_MULTIPLE_COUNTIES"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_SHOW_MARKET_SHARE_REPORT"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_MARKET_SHARE_REPORT"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_OLD_DI_DASHBOARD"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_OLD_DI_DASHBOARD"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_SHOW_ENGAGEMENT_TALLY_COLUMN"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_ENGAGEMENT_TALLY_COLUMN"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_SHOW_MARKET_SHARE_BACKUP_RESTORE"
      value = "/lecet-web/new-prod/REACT_APP_SHOW_MARKET_SHARE_BACKUP_RESTORE"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_ENABLE_ENGAGEMENT_TRACKED_TALLY_SORT"
      value = "/lecet-web/new-prod/REACT_APP_ENABLE_ENGAGEMENT_TRACKED_TALLY_SORT"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_GOOGLE_ANALYTICS_FOUR_TAG"
      value = "/lecet-web/new-prod/REACT_APP_GOOGLE_ANALYTICS_FOUR_TAG"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_FIREBASE_CONFIG_APP_ID"
      value = "/lecet-web/new-prod/REACT_APP_FIREBASE_CONFIG_APP_ID"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_FIREBASE_CONFIG_MESSAGING_SENDER_ID"
      value = "/lecet-web/new-prod/REACT_APP_FIREBASE_CONFIG_MESSAGING_SENDER_ID"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_FIREBASE_CONFIG_STORAGE_BUCKET"
      value = "/lecet-web/new-prod/REACT_APP_FIREBASE_CONFIG_STORAGE_BUCKET"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_FIREBASE_CONFIG_PROJECT_ID"
      value = "/lecet-web/new-prod/REACT_APP_FIREBASE_CONFIG_PROJECT_ID"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_FIREBASE_CONFIG_DATABASE_URL"
      value = "/lecet-web/new-prod/REACT_APP_FIREBASE_CONFIG_DATABASE_URL"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_FIREBASE_CONFIG_AUTH_DOMAIN"
      value = "/lecet-web/new-prod/REACT_APP_FIREBASE_CONFIG_AUTH_DOMAIN"
      type  = "PARAMETER_STORE"
    }


    environment_variable {
      name  = "REACT_APP_FIREBASE_CONFIG_API_KEY"
      value = "/lecet-web/new-prod/REACT_APP_FIREBASE_CONFIG_API_KEY"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "IMAGE_TAG"
      value = "new-prod"
    }

  }

  logs_config {
    cloudwatch_logs {
      group_name  = aws_cloudwatch_log_group.web_build_logs.name
      stream_name = "build"
    }
  }

  artifacts {
    type = "NO_ARTIFACTS"
  }

  tags = {
    Environment = var.environment
  }


}
