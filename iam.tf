##########################################################
# kOps: Administrative IAM (For Management EC2 Instance) #
##########################################################

# EC2 Instance assumes this role to allow kOps provisioning from the management server
data "aws_iam_policy_document" "prod_kops_admin_assume" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
  }
}

# Complete IAM policy document including SQS and EventBridge permissions
data "aws_iam_policy_document" "prod_kops_admin" {
  statement {
    actions = [
      "ec2:*",
      "s3:*",
      "route53:*",
      "iam:*",
      "cloudformation:*",
      "autoscaling:*",
      "elasticloadbalancing:*",
      "sqs:*",                         # Required for lifecycle hooks and kOps SQS queue management
      "events:*",                      # Required by kOps for EventBridge lifecycle rules
      "secretsmanager:GetSecretValue", # Required to retrieve SSH private key from Secrets Manager
      "ssm:PutParameter",              # Required to store kubeconfig in Parameter Store
      "ssm:GetParameter",              # Required to read parameters
      "ssm:GetParameters"              # Required to read multiple parameters
    ]
    resources = ["*"]
  }


}



# IAM Role assigned to the management EC2 instance for kOps
resource "aws_iam_role" "prod_kops_admin" {
  name               = "${var.environment}_kops-admin-role"
  assume_role_policy = data.aws_iam_policy_document.prod_kops_admin_assume.json

  tags = {
    Name        = "${var.environment}_kops-admin-role"
    Environment = var.environment
  }
}

# IAM policy for kOps admin with all necessary permissions
resource "aws_iam_policy" "prod_kops_admin_policy" {
  name        = "${var.environment}_kops-admin-policy"
  description = "Policy for ${var.environment} kOps admin"
  policy      = data.aws_iam_policy_document.prod_kops_admin.json

  tags = {
    Name        = "${var.environment}_kops-admin-policy"
    Environment = var.environment
  }
}

# Attach policy to IAM Role
resource "aws_iam_role_policy_attachment" "prod_kops_admin_attach" {
  role       = aws_iam_role.prod_kops_admin.name
  policy_arn = aws_iam_policy.prod_kops_admin_policy.arn
}

# EC2 Instance Profile for kOps management server to assume the IAM role
resource "aws_iam_instance_profile" "prod_kops_profile" {
  name = "${var.environment}_kops-instance-profile"
  role = aws_iam_role.prod_kops_admin.name

  tags = {
    Name        = "${var.environment}_kops-instance-profile"
    Environment = var.environment
  }
}

###########################################
# CodeBuild: CI/CD Permissions & Role     #
###########################################

# IAM Role for CodeBuild service
resource "aws_iam_role" "prod_codebuild" {
  name = "${var.environment}-codebuild-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = { Service = "codebuild.amazonaws.com" }
    }]
  })

  tags = {
    Environment = var.environment
  }
}

# Attach broad developer permissions for CodeBuild to the role
resource "aws_iam_role_policy_attachment" "prod_codebuild_basic" {
  role       = aws_iam_role.prod_codebuild.name
  policy_arn = "arn:aws:iam::aws:policy/AWSCodeBuildDeveloperAccess"

}

# --------------------------------------------------------------------
# DATA SOURCE: Retrieve AWS Account ID for policy ARNs below
# --------------------------------------------------------------------
data "aws_caller_identity" "current" {}

# Custom policy: permission to manage CloudWatch Logs for all CodeBuild log groups in this account/region
resource "aws_iam_policy" "prod_codebuild_cloudwatch_logs" {
  name        = "${var.environment}-CodeBuildCloudWatchLogs"
  description = "Allow CodeBuild to create and write CloudWatch Log streams"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = [
          "arn:aws:logs:${var.aws_region}:${data.aws_caller_identity.current.account_id}:log-group:/aws/codebuild/*",
          "arn:aws:logs:${var.aws_region}:${data.aws_caller_identity.current.account_id}:log-group:/aws/codedeploy/*"

        ]
      }
    ]
  })
  tags = {
    Environment = var.environment
  }
}

# Attach custom CloudWatch Logs policy to CodeBuild role
resource "aws_iam_role_policy_attachment" "prod_codebuild_attach_cloudwatch_logs" {
  role       = aws_iam_role.prod_codebuild.name
  policy_arn = aws_iam_policy.prod_codebuild_cloudwatch_logs.arn
}

# --- FULL ECR permissions for Docker authentication and image push/pull ---

resource "aws_iam_policy" "prod_codebuild_ecr_access" {
  name        = "${var.environment}-CodeBuildECRAccess"
  description = "Allow CodeBuild ECR login, push, and pull"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:PutImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:DescribeRepositories"
        ],
        Resource = "*"
      }
    ]
  })

  tags = {
    Environment = var.environment
  }
}

# Attach ECR policy to CodeBuild role
resource "aws_iam_role_policy_attachment" "prod_codebuild_ecr_access" {
  role       = aws_iam_role.prod_codebuild.name
  policy_arn = aws_iam_policy.prod_codebuild_ecr_access.arn
}

##########################################################
# CodeBuild: SSM Parameter Store Access for Deployments #
##########################################################

# IAM policy to allow CodeBuild to securely read kubeconfig and cert parameters from SSM Parameter Store
resource "aws_iam_policy" "prod_codebuild_ssm_access" {
  name        = "${var.environment}-CodeBuildSSMParameterAccess"
  description = "Allow CodeBuild to read kubeconfig and certificate parameters from SSM"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:GetParametersByPath"
        ],
        Resource = [
          "arn:aws:ssm:${var.aws_region}:${data.aws_caller_identity.current.account_id}:parameter/lecet/staging/*",
          "arn:aws:ssm:${var.aws_region}:${data.aws_caller_identity.current.account_id}:parameter/lecet/new-staging/*",
          "arn:aws:ssm:${var.aws_region}:${data.aws_caller_identity.current.account_id}:parameter/lecet-web/new-staging/*",
          "arn:aws:ssm:${var.aws_region}:${data.aws_caller_identity.current.account_id}:parameter/lecet/new-production/*",
          "arn:aws:ssm:${var.aws_region}:${data.aws_caller_identity.current.account_id}:parameter/lecet-web/new-production/*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "secretsmanager:GetSecretValue"
        ],
        Resource = [
          "arn:aws:secretsmanager:${var.aws_region}:${data.aws_caller_identity.current.account_id}:secret:/lecet/staging/*",
          "arn:aws:secretsmanager:${var.aws_region}:${data.aws_caller_identity.current.account_id}:secret:/lecet/new-prod/*"
        ]
      }
    ]
  })
  tags = {
    Environment = var.environment
  }
}

# Attach the SSM access policy to the existing CodeBuild IAM role
resource "aws_iam_role_policy_attachment" "prod_codebuild_ssm_access_attach" {
  role       = aws_iam_role.prod_codebuild.name
  policy_arn = aws_iam_policy.prod_codebuild_ssm_access.arn
}
