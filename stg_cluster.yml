apiVersion: kops.k8s.io/v1alpha2
kind: Cluster
metadata:
  name: new-staging.mdomain.xyz
spec:
  sshKeyName: new-stg-kops-key
  certificates:
    renewBefore: 720h # 30 days
    manageCertificates: true
  authentication:
    kubelet:
      enabled: true
    bootstrap:
      tokens: {}
  nodeAuthorizer: {}
  api:
    dns: {}
  authorization:
    rbac: {}
  channel: stable
  cloudProvider: aws
  configBase: s3://new-staging-k8s-lecet-org-state-store-001/new-staging.mdomain.xyz
  etcdClusters:
    - name: main
      etcdMembers:
        - name: a
          instanceGroup: master-us-east-1a
    - name: events
      etcdMembers:
        - name: a
          instanceGroup: master-us-east-1a
  iam:
    allowContainerRegistry: true
    legacy: false
  kubelet:
    anonymousAuth: false
    authenticationTokenWebhook: true
    authorizationMode: Webhook
  kubernetesApiAccess:
    - 0.0.0.0/0
  kubernetesVersion: 1.32.6
  masterPublicName: api.new-staging.mdomain.xyz
  networkCIDR: 10.150.0.0/16
  networking:
    kubenet: {}
  nonMasqueradeCIDR: 100.128.0.0/10
  sshAccess:
    - 10.150.0.0/16 # Basion/Kops Mangement CIDR
    - 38.74.197.217/32 # CT VPN
    - 35.80.16.107/32 # YML/BLR VPN
    - 0.0.0.0/0
  subnets:
    - cidr: 10.150.110.0/24
      name: us-east-1a
      type: Public
      zone: us-east-1a
    - cidr: 10.150.120.0/24
      name: us-east-1b
      type: Public
      zone: us-east-1b
    - cidr: 10.150.130.0/24
      name: us-east-1c
      type: Public
      zone: us-east-1c
  topology:
    dns:
      type: Public

---
apiVersion: kops.k8s.io/v1alpha2
kind: InstanceGroup
metadata:
  name: master-us-east-1a
  labels:
    kops.k8s.io/cluster: new-staging.mdomain.xyz
spec:
  role: Master
  machineType: t3.medium
  image: ami-0ca5a2f40c2601df6 # Updated Ubuntu 24.04 LTS AMI
  minSize: 1
  maxSize: 1
  kubelet:
    rotateCertificates: true
  nodeLabels:
    kops.k8s.io/instancegroup: master-us-east-1a
  subnets:
    - us-east-1a

---
apiVersion: kops.k8s.io/v1alpha2
kind: InstanceGroup
metadata:
  name: node
  labels:
    kops.k8s.io/cluster: new-staging.mdomain.xyz
spec:
  role: Node
  machineType: t3.medium
  image: ami-0ca5a2f40c2601df6 # Updated Ubuntu 24.04 LTS AMI
  minSize: 3
  maxSize: 4
  kubelet:
    rotateCertificates: true
  nodeLabels:
    kops.k8s.io/instancegroup: node
  subnets:
    - us-east-1a
    - us-east-1b
    - us-east-1c
