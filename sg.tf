resource "aws_security_group" "prod_kops_management" {
  name        = "prod_k8s-management-sg"
  description = "Allow SSH and HTTPS (Kubernetes API) access"
  vpc_id      = aws_vpc.prod_kops_mgmt.id

  ingress {
    description = "SSH"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.management_ssh_cidrs
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "prod_k8s-management-sg"
    Environment = var.environment
  }
}
