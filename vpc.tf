# VPC
resource "aws_vpc" "stg_kops_mgmt" {
  cidr_block           = var.vpc_cidr
  enable_dns_support   = true
  enable_dns_hostnames = true

  tags = {
    Name        = "stg-kops-mgmt-vpc"
    Environment = var.environment

  }
}

# Internet Gateway
resource "aws_internet_gateway" "stg_kops_mgmt" {
  vpc_id = aws_vpc.stg_kops_mgmt.id

  tags = {
    Name        = "stg-kops-mgmt-igw"
    Environment = var.environment

  }
}

# Public Subnet
resource "aws_subnet" "stg_kops_mgmt_public" {
  vpc_id                  = aws_vpc.stg_kops_mgmt.id
  cidr_block              = var.public_subnet_cidr
  availability_zone       = "${var.aws_region}a"
  map_public_ip_on_launch = true

  tags = {
    Name        = "stg-kops-mgmt-public-subnet"
    Environment = var.environment


  }
}

# Private Subnet
resource "aws_subnet" "stg_kops_mgmt_private" {
  vpc_id            = aws_vpc.stg_kops_mgmt.id
  cidr_block        = var.private_subnet_cidr
  availability_zone = "${var.aws_region}a"

  tags = {
    Name        = "stg-kops-mgmt-private-subnet"
    Environment = var.environment

  }
}

# Route Table for Public Subnet
resource "aws_route_table" "stg_kops_mgmt_public" {
  vpc_id = aws_vpc.stg_kops_mgmt.id

  tags = {
    Name        = "stg-kops-mgmt-public-rt"
    Environment = var.environment

  }
}

# Route to Internet from Public Subnet
resource "aws_route" "stg_kops_mgmt_public_internet_access" {
  route_table_id         = aws_route_table.stg_kops_mgmt_public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.stg_kops_mgmt.id
}

# Associate public subnet with public route table
resource "aws_route_table_association" "stg_kops_mgmt_public_assoc" {
  subnet_id      = aws_subnet.stg_kops_mgmt_public.id
  route_table_id = aws_route_table.stg_kops_mgmt_public.id
}
