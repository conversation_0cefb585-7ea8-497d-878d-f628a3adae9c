# CloudWatch Log Groups for CodeBuild

resource "aws_cloudwatch_log_group" "backend_base_build_logs" {
  name = "/aws/codebuild/new-staging-backend-base-build"
tags = {
    Environment = var.environment
  }
}

resource "aws_cloudwatch_log_group" "backend_build_logs" {
  name = "/aws/codebuild/new-staging-backend-build"
tags = {
    Environment = var.environment
  }
}

resource "aws_cloudwatch_log_group" "web_build_logs" {
  name = "/aws/codebuild/new-staging-web-build"
tags = {
    Environment = var.environment
  }
}

# CloudWatch Log Groups for CodeDeploy
resource "aws_cloudwatch_log_group" "backend_deploy_logs" {
  name = "/aws/codebuild/new-staging-backend-deploy"
tags = {
    Environment = var.environment
  }
}

resource "aws_cloudwatch_log_group" "web_deploy_logs" {
  name = "/aws/codebuild/new-staging-web-deploy"
tags = {
    Environment = var.environment
  }
}
