# CloudWatch Log Groups for CodeBuild

resource "aws_cloudwatch_log_group" "backend_base_build_logs" {
  name = "/aws/codebuild/new-prod-backend-base-build"
  tags = {
    Environment = var.environment
  }
}

resource "aws_cloudwatch_log_group" "backend_build_logs" {
  name = "/aws/codebuild/new-prod-backend-build"
  tags = {
    Environment = var.environment
  }
}

resource "aws_cloudwatch_log_group" "web_build_logs" {
  name = "/aws/codebuild/new-prod-web-build"
  tags = {
    Environment = var.environment
  }
}

# CloudWatch Log Groups for CodeDeploy
resource "aws_cloudwatch_log_group" "backend_deploy_logs" {
  name = "/aws/codebuild/new-prod-backend-deploy"
  tags = {
    Environment = var.environment
  }
}

resource "aws_cloudwatch_log_group" "web_deploy_logs" {
  name = "/aws/codebuild/new-prod-web-deploy"
  tags = {
    Environment = var.environment
  }
}
