# Automated kOps Kubernetes Cluster Deployment

## Prerequisites

1. AWS CLI installed and configured
2. Terraform installed
3. Appropriate AWS permissions for EC2, S3, IAM, Secrets Manager, and Parameter Store


## Security Features

- **Automatic SSH keypair generation** - No manual key management required
- **AWS Secrets Manager integration** - Private keys stored securely
- **Parameter Store automation** - Kubeconfig and certificates automatically stored
- **No secrets in repository** - All sensitive data managed by AWS services

## Deployment Steps

1. **Manully Create S3 bucket(new-stg-k8s-terraform-state-bucket) for Terraflrm state store and update same in prover.tf-backend section**
2. **Run Terraform deployment** (handles everything below automatically)

### Terraform Deployment

   - terraform init
   - terraform plan
   - terraform apply
handles everything below automatically:
   - Creates new SSH keypair
   - Stores private key in AWS Secrets Manager
   - Deploys kOps management server
   - Creates Kubernetes cluster
   - Generates and stores kubeconfig in Parameter Store
3. **GitHub Classic PAT** Used existing PAT and imported in AWS Region where we run CodeBuild

4. # Validate cluster with extended wait time

Once connected to the management server, run these commands:

export KOPS_STATE_STORE=s3://new-staging-k8s-lecet-org-state-store

kops validate cluster --wait 10m

# Check cluster nodes

kubectl get nodes

#  State Store Buckets
kops statestore bucket: new-staging-k8s-lecet-org-state-store
terrafrom state store bucket: new-stg-k8s-terraform-state-bucket
# Changes required in Code Repos:
5. Map New ECR Repo names in BE & FE github - docker-compse.yaml and buildspec.yml files
6. Login to AWS Console and Run codebuild and complete codbuild and ensure all images pushed to new ECR repos

7. Connect to management server and create/apply all BE and FE manifests( namespace mepgo, configmaps,secrets,deployments,services) mainfites and ensure new ecr repo images are referenced in the manifest files

kubectl apply -f <namespace.yaml>
kubectl apply -f <configmap.yaml> -n mepgo
kubectl apply -f <secret.yaml> -n mepgo
kubectl apply -f <deployment.yaml> -n mepgo
kubectl apply -f <service.yaml> -n mepgo

8. Login to AWS Console and Run codebuild and Codedeploy process for FE and BE
9. Logs configured under Cloudwatch log groups
10. Add/Update DNS records to map new cluster/services(FE/BE)

##########################################Delete/Destroy Process######################### 

7. # Delete Cluster and Destroy kops managment server & infra

# Delete Cluster - Manually

connect to new staging kops managmeent server
export KOPS_STATE_STORE=s3://new-staging-k8s-lecet-org-state-store
kops delete cluster --name new-staging.lecet.org  --yes

# Post successful deletion of the clsuter run terraform destroy

terrafrom destroy -auto-approve
