# New Production Environment Setup

This document describes the production environment setup using the "new-prod" naming convention. This production environment is designed to work alongside the existing staging environment without conflicts.

## Overview

The production environment has been configured with the following key characteristics:

- **Environment Name**: `new-prod`
- **VPC CIDR**: `10.160.0.0/16` (different from staging to avoid conflicts)
- **Cluster Name**: `new-prod.mdomain.xyz`
- **S3 State Store**: `new-prod-k8s-lecet-org-state-store-001`
- **ECR Repositories**: Shared with staging environment (same repository names)

## Key Files

### Configuration Files
- `terraform-prod.tfvars` - Production-specific Terraform variables
- `prod_cluster.yml` - Production Kubernetes cluster configuration

### Infrastructure Files
All Terraform files have been updated to use "new-prod" naming convention:
- `vpc.tf` - VPC and networking resources
- `ec2.tf` - Management server configuration
- `sg.tf` - Security groups
- `s3.tf` - S3 state store bucket
- `iam.tf` - IAM roles and policies
- `keypair.tf` - SSH key management
- `codebuild.tf` - CodeBuild projects for CI/CD
- `codedeploy.tf` - CodeDeploy projects for Kubernetes deployment
- `clwatchlog.tf` - CloudWatch log groups
- `parameter-store.tf` - Parameter store configuration
- `locals.tf` - Local variables

## Network Configuration

### VPC and Subnets
- **VPC CIDR**: `10.160.0.0/16`
- **Public Subnet**: `10.160.10.0/24`
- **Private Subnet**: `10.160.20.0/24`
- **Kubernetes Subnets**:
  - `us-east-1a`: `10.160.110.0/24`
  - `us-east-1b`: `10.160.120.0/24`
  - `us-east-1c`: `10.160.130.0/24`

## ECR Repository Sharing

The production environment uses the same ECR repositories as staging:
- `lecet_backend`
- `lecet_celery`
- `lecet_celery-beat`
- `lecet_flower`
- `lecet_web`
- `lecet_backendbase`

Images are tagged with `new-prod` to differentiate from staging images tagged with `new-staging`.

## Parameter Store Paths

All parameter store paths use the `/lecet-web/new-prod/` prefix:
- Kubernetes configuration: `/lecet-web/new-prod/KUBECONFIGDATA`
- Application settings: `/lecet-web/new-prod/[PARAMETER_NAME]`

## Secrets Manager Paths

SSH keys and secrets use the `/lecet/new-prod/` prefix:
- Private key: `/lecet/new-prod/new-prod-kops-ssh-private-key`
- Public key: `/lecet/new-prod/new-prod-kops-ssh-public-key`

## Deployment Instructions

1. **Initialize Terraform with production variables**:
   ```bash
   terraform init
   terraform plan -var-file="terraform-prod.tfvars"
   terraform apply -var-file="terraform-prod.tfvars"
   ```

2. **Verify cluster deployment**:
   - Check the management server logs
   - Verify cluster status via kubectl
   - Confirm parameter store values are populated

3. **Deploy applications**:
   - Use CodeBuild projects with `new-prod` naming
   - Images will be tagged with `new-prod`
   - Deployments will use production parameter store values

## Key Differences from Staging

| Component | Staging | Production |
|-----------|---------|------------|
| Environment | `new-staging` | `new-prod` |
| VPC CIDR | `10.150.0.0/16` | `10.160.0.0/16` |
| Cluster Name | `new-staging.mdomain.xyz` | `new-prod.mdomain.xyz` |
| S3 Bucket | `new-staging-k8s-lecet-org-state-store-001` | `new-prod-k8s-lecet-org-state-store-001` |
| Image Tags | `new-staging` | `new-prod` |
| Parameter Paths | `/lecet-web/new-staging/` | `/lecet-web/new-prod/` |
| SSH Key Name | `new-stg-kops-key` | `new-prod-kops-key` |

## Resource Isolation

The production environment is completely isolated from staging:
- Separate VPC with different CIDR ranges
- Separate S3 state store bucket
- Separate parameter store paths
- Separate secrets manager paths
- Separate CodeBuild/CodeDeploy projects
- Separate CloudWatch log groups

Only ECR repositories are shared between environments, with different image tags for isolation.

## Security Considerations

- Same VPN and office IP restrictions as staging
- Separate IAM roles and policies for production
- Isolated network infrastructure
- Separate SSH key pairs for access control

## Monitoring and Logging

- CloudWatch log groups use `new-prod` naming convention
- All resources are tagged with `Environment = new-prod`
- Separate monitoring and alerting can be configured per environment
