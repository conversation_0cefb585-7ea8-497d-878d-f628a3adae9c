resource "aws_instance" "stg_kops_mgmt" {
  ami                         = var.management_ami_id
  instance_type               = var.management_instance_type
  key_name                    = aws_key_pair.stg_kops_keypair.key_name
  iam_instance_profile        = aws_iam_instance_profile.stg_kops_profile.name
  subnet_id                   = aws_subnet.stg_kops_mgmt_public.id
  vpc_security_group_ids      = [aws_security_group.stg_kops_management.id]
  associate_public_ip_address = true

  tags = {
    Name        = "New-Staging-Kops-Management-Server"
    Environment = var.environment
  }

  provisioner "file" {
    source      = "stg_cluster.yml"
    destination = "/home/<USER>/stg_cluster.yml"
    connection {
      type        = "ssh"
      user        = "ubuntu"
      private_key = data.aws_secretsmanager_secret_version.stg_kops_private_key.secret_string
      host        = self.public_ip
      timeout     = "20m"
    }
  }

  provisioner "remote-exec" {
    connection {
      type        = "ssh"
      user        = "ubuntu"
      private_key = data.aws_secretsmanager_secret_version.stg_kops_private_key.secret_string
      host        = self.public_ip
      timeout     = "20m"
    }

    inline = [
      # --- OS Dependencies and Tooling ---
      "sudo apt-get update -y",
      "sudo apt-get install -y curl unzip",

      # --- Install latest kOps binary ---
      "curl -LO 'https://github.com/kubernetes/kops/releases/latest/download/kops-linux-amd64'",
      "chmod +x kops-linux-amd64",
      "sudo mv kops-linux-amd64 /usr/local/bin/kops",

      # --- Install specific kubectl version (v1.33.0) ---
      "curl -LO https://dl.k8s.io/release/v1.33.0/bin/linux/amd64/kubectl",
      "chmod +x kubectl",
      "sudo mv kubectl /usr/local/bin/kubectl",

      # --- Cluster Deployment Section ---
      "echo '=== Set your S3 state store and create cluster using cluster.yml ==='",
      "export KOPS_STATE_STORE=s3://${var.kops_state_store_bucket} && /usr/local/bin/kops create -f /home/<USER>/stg_cluster.yml",

      "echo '=== Apply the cluster configuration ==='",
      "export KOPS_STATE_STORE=s3://${var.kops_state_store_bucket} && /usr/local/bin/kops update cluster --name ${var.cluster_name} --yes",

      # --- Wait for cluster readiness ---
      "echo '=== Waiting for cluster to be ready ==='",
      "echo 'Note: DNS propagation and node readiness can take 10-15 minutes for new clusters'",
      "export KOPS_STATE_STORE=s3://${var.kops_state_store_bucket}",
      "CLUSTER_READY=false",
      "for i in {1..20}; do",
      "  echo \"Validation attempt $i/20: Checking cluster status...\"",
      "  if /usr/local/bin/kops validate cluster --name ${var.cluster_name} --wait 30s 2>/dev/null; then",
      "    echo 'SUCCESS: Cluster is ready and validated!'",
      "    CLUSTER_READY=true",
      "    break",
      "  else",
      "    echo \"Cluster not ready yet (attempt $i/20). Reasons might be DNS propagation, node join, control plane init.\"",
      "    if [ $i -lt 20 ]; then",
      "      echo 'Waiting 45 seconds before next attempt...'",
      "      sleep 45",
      "    fi",
      "  fi",
      "done",
      "if [ \"$CLUSTER_READY\" = \"false\" ]; then",
      "  echo 'WARNING: Cluster validation timed out after 15 minutes'",
      "  echo 'Cluster may still be initializing - continuing with setup'",
      "fi",

      # --- Generate kubeconfig with 5 year admin cert validity ---
      "echo '=== Generating kubeconfig with 5 years admin cert validity ==='",
      "export KOPS_STATE_STORE=s3://${var.kops_state_store_bucket}",
      "if /usr/local/bin/kops export kubeconfig --admin=43800h --name ${var.cluster_name}; then",
      "  echo 'SUCCESS: Kubeconfig generated successfully'",
      "else",
      "  echo 'WARNING: Failed to generate kubeconfig - cluster may still be initializing'",
      "  echo 'Trying fallback kubeconfig generation...'",
      "  mkdir -p ~/.kube",
      "  /usr/local/bin/kops export kubeconfig --name ${var.cluster_name} --admin=43800h || echo 'Fallback kubeconfig generation failed'",
      "fi",

      # --- Backup kubeconfig ---
      "echo '=== Creating kubeconfig backup and extracting certs ==='",
      "mkdir -p /home/<USER>/config_backup",
      "if [ -f ~/.kube/config ]; then",
      "  cp ~/.kube/config /home/<USER>/config_backup/new_stg_config",
      "  echo 'SUCCESS: Kubeconfig backed up'",
      "else",
      "  echo 'WARNING: Kubeconfig not found - creating dummy config file'",
      "  echo 'dummy-config-data' > /home/<USER>/config_backup/new_stg_config",
      "fi",

      # --- Extract client cert and key base64 data (no decode) ---
      "CLIENT_KEY_B64=$(kubectl config view --raw --kubeconfig=/home/<USER>/config_backup/new_stg_config -o jsonpath='{.users[0].user.client-key-data}')",
      "CLIENT_CERT_B64=$(kubectl config view --raw --kubeconfig=/home/<USER>/config_backup/new_stg_config -o jsonpath='{.users[0].user.client-certificate-data}')",

      "if [ -z \"$CLIENT_KEY_B64\" ]; then",
      "  echo 'ERROR: Client key base64 data empty - creating dummy key file'",
      "  echo 'dummy-key-data' > /home/<USER>/config_backup/key.pem.b64",
      "else",
      "  echo \"$CLIENT_KEY_B64\" | tr -d '\\n' > /home/<USER>/config_backup/key.pem.b64",
      "  echo 'SUCCESS: Client key base64 extracted'",
      "fi",

      "if [ -z \"$CLIENT_CERT_B64\" ]; then",
      "  echo 'ERROR: Client cert base64 data empty - creating dummy cert file'",
      "  echo 'dummy-cert-data' > /home/<USER>/config_backup/cert.pem.b64",
      "else",
      "  echo \"$CLIENT_CERT_B64\" | tr -d '\\n' > /home/<USER>/config_backup/cert.pem.b64",
      "  echo 'SUCCESS: Client cert base64 extracted'",
      "fi",

      # --- Base64 encode kubeconfig file ---
      "base64 /home/<USER>/config_backup/new_stg_config > /home/<USER>/config_backup/new_stg_config.b64",
      "echo 'SUCCESS: Kubeconfig base64 encoded (old logic)'",

      # --- Install AWS CLI v2 ---
      "echo '=== Installing AWS CLI v2 ==='",
      "curl 'https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip' -o 'awscliv2.zip'",
      "unzip awscliv2.zip",
      "sudo ./aws/install",

      # --- Upload all three parameters to Parameter Store ---
      "echo '=== Uploading KUBECONFIGDATA (base64 encoded kubeconfig) ==='",
      "if aws ssm put-parameter --name '/lecet-web/new-staging/KUBECONFIGDATA' --type SecureString --value \"$(tr -d '\\n' < /home/<USER>/config_backup/new_stg_config.b64)\" --tier Advanced --overwrite --region us-east-1 2>/dev/null; then",
      "  echo 'SUCCESS: KUBECONFIGDATA uploaded to Parameter Store'",
      "else",
      "  echo 'ERROR: Failed to upload KUBECONFIGDATA to Parameter Store'",
      "fi",

      "echo '=== Uploading KUBECONFIGKEY (client key base64) ==='",
      "if aws ssm put-parameter --name '/lecet-web/new-staging/KUBECONFIGKEY' --type SecureString --value \"$(cat /home/<USER>/config_backup/key.pem.b64)\" --tier Advanced --overwrite --region us-east-1 2>/dev/null; then",
      "  echo 'SUCCESS: KUBECONFIGKEY uploaded to Parameter Store'",
      "else",
      "  echo 'ERROR: Failed to upload KUBECONFIGKEY to Parameter Store'",
      "fi",

      "echo '=== Uploading KUBECONFIGCERT (client cert base64) ==='",
      "if aws ssm put-parameter --name '/lecet-web/new-staging/KUBECONFIGCERT' --type SecureString --value \"$(cat /home/<USER>/config_backup/cert.pem.b64)\" --tier Advanced --overwrite --region us-east-1 2>/dev/null; then",
      "  echo 'SUCCESS: KUBECONFIGCERT uploaded to Parameter Store'",
      "else",
      "  echo 'ERROR: Failed to upload KUBECONFIGCERT to Parameter Store'",
      "fi",

      # --- Finalize ---
      "echo ''",
      "echo '========================================='",
      "echo '=== MANAGEMENT SERVER SETUP COMPLETE ==='",
      "echo '========================================='",
      "echo ''",
      "echo 'Management server setup completed successfully!'",
      "echo 'Kubeconfig and certificates have been uploaded to Parameter Store'",
      "echo 'SSH private key is available in AWS Secrets Manager at: /lecet/new-staging/new-stg-kops-ssh-private-key'",
    ]
  }
}

output "management_server_public_ip" {
  description = "Public IP address of the kOps management server"
  value       = aws_instance.stg_kops_mgmt.public_ip
}

output "management_server_private_ip" {
  description = "Private IP address of the kOps management server"
  value       = aws_instance.stg_kops_mgmt.private_ip
}
